{"name": "phonebook_backend", "version": "1.0.0", "description": "Backend for phonebook app", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "test": "echo \"Error: no test specified\" && exit 1", "build:ui": "rm -rf dist && cd ../../part2/phonebook && npm run build && cp -r dist ../../part3/phonebook_backend", "deploy:full": "npm run build:ui && git add . && git commit -m buildui && git push"}, "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^5.1.0", "mongoose": "^8.15.2", "morgan": "^1.10.0"}}