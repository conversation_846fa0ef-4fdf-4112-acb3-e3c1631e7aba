GET http://localhost:3002/api/notes/68700

###
POST http://localhost:3002/api/notes/
Content-Type: application/json

{ 
    "content": "Ch"
    
}

###
GET http://localhost:3002/api/notes/

###
GET https://fso-twwa.onrender.com/api/notes/1

###
DELETE http://localhost:3002/api/notes/6871772b1b7e7c7

###
PUT http://localhost:3002/api/notes/687173171153cbe4bd602be5
Content-Type: application/json

{ 
    "content": "Chunky Libro",
    "important": true
    
}